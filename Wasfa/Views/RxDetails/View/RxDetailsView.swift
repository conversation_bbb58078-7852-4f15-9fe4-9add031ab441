//
//  RxDetailsView.swift
//  Wasfa
//
//  Created by Apple on 21/02/2025.
//

import SwiftUI

struct RxDetailsView: View {
    let rxID: Int
    @StateObject private var viewModel: RxDetailsViewModel = .init()
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "Rx Details") {
                LazyVStack(spacing: 12) {
//                    foreach view

                    if viewModel.rxDetailsModelList.isEmpty {
                        ContentUnavailableView(
                            "No Rx Details Available",
                            systemImage: "doc.text.magnifyingglass",
                            description: Text("There are no data found at the moment. Please check back later.")
                        )
                        .padding()
                    } else {
                        ForEach(viewModel.rxDetailsModelList) {
                            RxProductCard(model: $0)
                        }
                    }
                }
                .padding(.vertical)
            }
            .background(ColorConstants.WhiteA700)
        }
        .onLoad {viewModel.getRxDetails(id: rxID)}
    }
}

#Preview {
    NavigationStack {
        RxDetailsView(rxID: 3).attachAllEnvironmentObjects()
    }
}
