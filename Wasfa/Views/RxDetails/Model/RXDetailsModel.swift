//
//  RXDetailsModel.swift
//  Wasfa
//
//  Created by Apple on 22/02/2025.
//

import Foundation


// MARK: - Datum
struct RxHistoryModel: Codable, Identifiable {
    let address: Address
    let deliveryStatus: String
    let id: Int
    let code, paymentType, date, paymentStatus: String
    let grandTotal: String
    let productImages: [String]
    let totalProductCount: Int
    let deliveryStatusLabel: String
}

// MARK: - RXDetailsModel
struct RXDetailsModel: Codable, Identifiable {
    let id: Int
    let productName: String?
    let thumbnailImage: String?
    let dosage: String?
    let duration, doseTime, description: String?
    let price: String
    let quantity: Int

    enum CodingKeys: String, CodingKey {
        case id, productName, thumbnailImage, dosage, duration
        case doseTime = "dose_time"
        case description, price, quantity
    }
}




