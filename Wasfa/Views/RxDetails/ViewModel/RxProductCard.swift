//
//  RxProductCard.swift
//  Wasfa
//
//  Created by Apple on 21/02/2025.
//

import SwiftUI

struct RxProductCard: View {
    let model:RXDetailsModel
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 16) {
                NetworkImageView(path: model.thumbnailImage)
                    .frame(width: getRelativeWidth(90.0), height: getRelativeHeight(90.0),
                           alignment: .leading)
                    .scaledToFit()
                    .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                               bottomRight: 8.0).fill(.clear))
                VStack(alignment: .leading, spacing: 8) {
                    Text(model.productName ?? "")
                        .font(FontScheme.kRobotoRomanMedium(size: getRelativeHeight(13.0)))
                        .fontWeight(.medium)
                        .foregroundColor(ColorConstants.Black90001)
                        .multilineTextAlignment(.leading)
                    Text("KD \(model.price)")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Gray600)
                        .multilineTextAlignment(.leading)
                    Text("QTY : \(model.quantity)")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Gray600)
                        .multilineTextAlignment(.leading)
                }
            }

            HStack(spacing: 8) {
                VStack(alignment: .leading, spacing: 8){
                    Text("Dosage")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                        .foregroundColor(ColorConstants.Black900)
                    Text("\(model.dosage ?? "0")")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .foregroundColor(ColorConstants.Gray600)
                        .frame(maxWidth: .infinity, maxHeight: 20)
                        .padding(8)
                        .background(RoundedRectangle(cornerRadius: 8).stroke(Color.gray, lineWidth: 1))
                        .multilineTextAlignment(.center)
                }
                            
                VStack(alignment: .leading, spacing: 8){
                    Text("Duration")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                        .foregroundColor(ColorConstants.Black900)
                    Text(model.duration ?? "")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .foregroundColor(ColorConstants.Gray600)
                        .frame(maxWidth: .infinity, maxHeight: 20)
                        .padding(8)
                        .background(RoundedRectangle(cornerRadius: 8).stroke(Color.gray, lineWidth: 1))
                        .multilineTextAlignment(.center)
                }
                            
                VStack(alignment: .leading, spacing: 8){
                    Text("Dose Time")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                        .foregroundColor(ColorConstants.Black900)
                    Text(model.doseTime ?? "")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .foregroundColor(ColorConstants.Gray600)
                        .frame(maxWidth: .infinity, maxHeight: 20)
                        .padding(8)
                        .background(RoundedRectangle(cornerRadius: 8).stroke(Color.gray, lineWidth: 1))
                        .multilineTextAlignment(.center)
                }
            }
            
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(RoundedCorners(topLeft: 22.0, topRight: 22.0, bottomLeft: 22.0,
                                   bottomRight: 22.0)
                .fill(ColorConstants.WhiteA700).shadow(color: ColorConstants.Black9001e, radius: 5, x: 0, y: 0))
        .padding(.horizontal)
    }
}

#Preview {
//    RxProductCard()
}
