import SwiftUI

struct SignInView: View {
    @StateObject var viewModel = SignInViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @FocusState private var isFocused: Bool
    var body: some View {
        SuperView(pageState: self.$viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: StringConstants.kLblSignIn) {
                VStack {
                    Text("Welcome back! Glad to see you, Again!")
                        .font(FontScheme.kPoppins(size: 30.relativeFontSize))
                        .fontWeight(.bold)
                        .foregroundColor(Color(hex: "#1E232C"))
                        .multilineTextAlignment(.leading)

                    VStack(alignment: .leading, spacing: 0) {
                        HStack {
                            HStack {
//                                Text("+965")
//                                    .font(FontScheme.kPoppins(size: 15.relativeFontSize))
//                                    .fontWeight(.medium)
//                                    .foregroundColor(ColorConstants.Black90001)
//                                    .padding()
                                CountryCodePickerView(selectedCountryCode: self.$viewModel.selectedCountryCode)
                            }
                            .frame(height: getRelativeHeight(56.0),
                                   alignment: .leading)
                            .overlay(RoundedRectangle(cornerRadius: 8.relativeFontSize)
                                .stroke(Color(hex: "#E8ECF4"),
                                        lineWidth: 1))
                            .background(RoundedRectangle(cornerRadius: 8.relativeFontSize)
                                .fill(Color(hex: "#F7F8F9")))
                            .padding(.top, getRelativeHeight(6.0))

                            HStack {
                                TextField("Phone Number",
                                          text: self.$viewModel.phoneText)
                                    .keyboardToolbar  {
                                        self.isFocused = false
                                    }
                                    .font(FontScheme.kPoppins(size: 15.relativeFontSize))
//                                    .keyboardType(.numberPad)
                                    .submitLabel(.done)
                                    .focused(self.$isFocused)
                                    .fontWeight(.medium)
                                    .foregroundColor(ColorConstants.Black90001)
                                    .padding()
                            }
                            .frame(height: getRelativeHeight(56.0),
                                   alignment: .leading)
                            .frame(maxWidth: .infinity)
                            .overlay(RoundedRectangle(cornerRadius: 8.relativeFontSize)
                                .stroke(Color(hex: "#E8ECF4"),
                                        lineWidth: 1))
                            .background(RoundedRectangle(cornerRadius: 8.relativeFontSize)
                                .fill(Color(hex: "#F7F8F9")))
                            .padding(.top, getRelativeHeight(6.0))
                        }
                    }
                    .padding(.horizontal, getRelativeHeight(16.0))
                    .padding(.top, getRelativeHeight(10.0))

                    Button(action: self.viewModel.sendOtp, label: {
                        HStack(spacing: 0) {
                            Text("Send OTP")
                                .font(FontScheme.kPoppins(size: 20.relativeFontSize))
                                .fontWeight(.semibold)
                                .padding(.horizontal, getRelativeWidth(30.0))
                                .padding(.vertical, getRelativeHeight(14.0))
                                .foregroundColor(ColorConstants.WhiteA700)
                                .multilineTextAlignment(.center)
                                .frame(width: getRelativeWidth(348.0),
                                       height: getRelativeHeight(56.0), alignment: .center)
                                .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
                                                           bottomLeft: 8.0, bottomRight: 8.0)
                                        .fill(ColorConstants.Blue300))
//                                .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
                        }
                    })
                    .disableWithOpacity(self.viewModel.phoneText.isEmpty)
                    .padding(.top, getRelativeHeight(24.0))
                }
                .padding(.vertical, getRelativeHeight(32))
                .background(ColorConstants.WhiteA700)
            }
        }
        .injectEnvironmentValues(self.viewModel)
    }
}

struct SigninView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationStack {
            SignInView().attachAllEnvironmentObjects()
        }
    }
}

struct KeyboardToolbar: ViewModifier {
    var onDone: () -> Void

    func body(content: Content) -> some View {
        content
            .toolbar {
                ToolbarItem(placement: .keyboard) {
                    HStack {
                        Spacer()
                        Button("Done") {
                            self.onDone()
                        }
                        .tint(Color(UIColor.systemBlue))
                    }
                }
            }
    }
}

extension View {
    func keyboardToolbar(onDone: @escaping () -> Void) -> some View {
        self.modifier(KeyboardToolbar(onDone: onDone))
    }
}
