//
//  AboutUsViewModel.swift
//  Wasfa
//
//  Created by Apple on 18/02/2025.
//

import SwiftUI

class AboutUsViewModel: SuperViewModel {
    @Published var aboutUs: AboutUsModel?
    @Published var faqModelList: [FAQModel] = []


    func getAboutUs() {
        onApiCall(api.aboutUs, parameters: emptyDictionary) {
            self.aboutUs = $0.data
        }
    }
    
    func getFAQ() {
        onApiCall(api.faq, parameters: emptyDictionary) { 
            self.faqModelList = $0.data ?? []
//            self.faqModelList = [.init(id: 1, quesion: String(repeating: "How to create a account?", count: 5), answer: "Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old."), .init(id: 2, quesion: "How to add a payment method by this app?", answer: "Test Answer 2")]
        }
    }
    
    
    func getTermsAndConditions() {
        onApiCall(api.termsAndConditions, parameters: emptyDictionary) {
            self.aboutUs = $0.data
        }
    }
    
    func getPrivacyPolicy() {
        onApiCall(api.privacyPolicy, parameters: emptyDictionary) {
            self.aboutUs = $0.data
        }
    }
}
