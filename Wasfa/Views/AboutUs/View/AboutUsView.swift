//
//  AboutUsView.swift
//  Wasfa
//
//  Created by Apple on 18/02/2025.
//

import SwiftUI

struct AboutUsView: View {
    @StateObject private var viewModel = AboutUsViewModel()
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "About Us", hideBackButton: false) {
                VStack {
                    if let content = viewModel.aboutUs?.content {
                        CustomRichText(html: content, fontFamily: "NunitoLight", fontSrc: "NunitoLight.ttf", sizeAdjust: "100", fontColor: ColorConstants.Black90001, lineHeight: 110.0.relativeFontSize)
                    }
                }
                .padding()
            }
        }.onLoad(perform: viewModel.getAboutUs)
    }
}

struct FAQView: View {
    @StateObject private var viewModel = AboutUsViewModel()
    @State private var expandedItems: Set<Int> = []

    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "FAQ", hideBackButton: false) {
                VStack(spacing: 16) {
                    if viewModel.faqModelList.isEmpty {
                        ContentUnavailableView("FAQ list is empty!", systemImage: "questionmark.circle", description: Text("No frequently asked questions are available at the moment."))
                    } else {
                        LazyVStack(spacing: 12) {
                            ForEach(viewModel.faqModelList) { faq in
                                ExpandableFAQItem(
                                    faq: faq,
                                    isExpanded: expandedItems.contains(faq.id)
                                ) {
                                    toggleExpansion(for: faq.id)
                                }
                            }
                        } .onLoad {
                            if let id = viewModel.faqModelList.first?.id { toggleExpansion(for: id)}
                            
                        }
                    }
                }
                .padding()
               
            }
        }.onLoad(perform: viewModel.getFAQ)
    }

    private func toggleExpansion(for id: Int) {
        withAnimation(.easeInOut(duration: 0.3)) {
            if expandedItems.contains(id) {
                expandedItems.remove(id)
            } else {
                expandedItems.insert(id)
            }
        }
    }
}

struct TermsAndConditionsView: View {
    @StateObject private var viewModel = AboutUsViewModel()
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "Terms And Conditions", hideBackButton: false) {
                VStack {
                    if let content = viewModel.aboutUs?.content {
                        CustomRichText(html: content, fontFamily: "NunitoLight", fontSrc: "NunitoLight.ttf", sizeAdjust: "100", fontColor: ColorConstants.Black90001, lineHeight: 110.0.relativeFontSize)
                    }
                }
                .padding()
            }
        }.onLoad(perform: viewModel.getTermsAndConditions)
    }
}

struct PrivacyPolicyView: View {
    @StateObject private var viewModel = AboutUsViewModel()
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "Privacy Policy", hideBackButton: false) {
                VStack {
                    if let content = viewModel.aboutUs?.content {
                        CustomRichText(html: content, fontFamily: "NunitoLight", fontSrc: "NunitoLight.ttf", sizeAdjust: "100", fontColor: ColorConstants.Black90001, lineHeight: 110.0.relativeFontSize)
                    }
                }
                .padding()
            }
        }.onLoad(perform: viewModel.getPrivacyPolicy)
    }
}

// MARK: - Expandable FAQ Item Component
struct ExpandableFAQItem: View {
    let faq: FAQModel
    let isExpanded: Bool
    let onTap: () -> Void

    var body: some View {
        VStack(spacing: 0) {
            // FAQ Question Header
            Button(action: onTap) {
                HStack {
                    Text(faq.quesion)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(16)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Black90001)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    Spacer()

                    // Chevron Icon
                    Image(systemName: isExpanded ? "minus" : "plus")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(ColorConstants.Blue600)
                        .rotationEffect(.degrees(isExpanded ? 360 : 0))
                        
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 16)
//                .background(Color(red: 0.95, green: 0.97, blue: 1))
              
                .clipShape(RoundedRectangle(cornerRadius: 12))
                
            }
         

            // FAQ Answer Content
            if isExpanded {
                VStack(alignment: .leading, spacing: 8) {
                    Text(faq.answer)
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(14)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.DescriptionText)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .lineLimit(nil)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
               
                .cornerRadius(12)
//                .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                .transition(.asymmetric(
                    insertion: .opacity.combined(with: .move(edge: .top)),
                    removal: .opacity.combined(with: .move(edge: .top))
                ))
            }
        }
        .background(Color.white)
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .overlay(RoundedRectangle(cornerRadius: 8).stroke(Color(hex: "#E8EBE6"), lineWidth: 1))
        .shadow(color: Color.black.opacity(0.08), radius: 4, x: 0, y: 2)
        
        .animation(.bouncy, value: isExpanded)
    }
}
