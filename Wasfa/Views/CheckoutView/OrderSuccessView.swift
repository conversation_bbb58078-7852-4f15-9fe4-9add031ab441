//
//  OrderSuccssView.swift
//  Wasfa
//
//  Created by Apple on 27/11/2024.
//

import SwiftUI

struct OrderSuccessView: View {
    @EnvironmentObject private var appState: AppState
    @Environment(\.routerManager) private var routerManager: RouterManager

    var body: some View {
        VStack {
            VStack {
                Image("img_success")
                    .resizable()
                    .frame(width: getRelativeWidth(152.0), height: getRelativeWidth(152.0),
                           alignment: .center)
                    .scaledToFit()
                    .clipped()
                Text("Order Place Successfully")
                    .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(24.0)))
                    .fontWeight(.heavy)
                    .foregroundColor(ColorConstants.Black90001)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(227.0), height: getRelativeHeight(33.0),
                           alignment: .topLeading)
                    .padding(.top, getRelativeHeight(22.0))
                Text("Your order is placed successfully, Please check your Order History in my account for further details.")
                    .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(14.0)))
                    .fontWeight(.regular)
                    .foregroundColor(ColorConstants.Black90001)
                    .multilineTextAlignment(.center)
                    .padding([.top, .horizontal], getRelativeHeight(18.0))
                Button(action: {
                    let routesType: RoutesType = routerManager.mapRouterWithTab(appState: appState)
                    routerManager.popToRoot(where: routesType)

                }, label: {
                    HStack(spacing: 0) {
                        Text("Back to Home")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(14.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(348.0), height: getRelativeHeight(54.0),
                                   alignment: .center)
                            .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                                       bottomRight: 8.0)
                                    .fill(ColorConstants.Blue300))
                            .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
                            .padding(.vertical, getRelativeHeight(25.0))
                    }
                })
            }

            .background(ColorConstants.WhiteA700)
            .padding(.top, getRelativeHeight(30.0))
            .padding(.bottom, getRelativeHeight(10.0))
        }
        .onLoad(perform: { appState.updateCartQuantity(0) })
        
        .background(ColorConstants.WhiteA700)
        .hideNavigationBar()
        .background(DisableSwipeBack())
    }
}


#Preview {
    NavigationStack {
        OrderSuccessView()
    }
}



// UIViewControllerRepresentable to access UINavigationController and disable swipe back
struct DisableSwipeBack: UIViewControllerRepresentable {
    func makeUIViewController(context: Context) -> UIViewController {
        let controller = UIViewController()
        DispatchQueue.main.async {
            controller.navigationController?.interactivePopGestureRecognizer?.isEnabled = false
        }
        return controller
    }
    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {}
}
